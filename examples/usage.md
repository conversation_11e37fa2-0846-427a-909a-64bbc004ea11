# Intercom MCP Server Usage Examples

This document provides practical examples of how to use the Intercom MCP Server tools.

## Prerequisites

1. Set up your environment variables:
```bash
export INTERCOM_ACCESS_TOKEN="your_intercom_token"
export OPENAI_API_KEY="your_openai_key"
```

2. Start the MCP server:
```bash
npm start
```

## Tool Usage Examples

### 1. List Conversations

#### Get all conversations from the last 30 days
```json
{
  "tool": "list-conversations",
  "arguments": {
    "dateRange": {
      "startDate": "2024-01-01T00:00:00Z",
      "endDate": "2024-01-31T23:59:59Z"
    },
    "pagination": {
      "per_page": 20
    }
  }
}
```

#### Get unrated conversations only
```json
{
  "tool": "list-conversations",
  "arguments": {
    "hasRating": false,
    "status": ["closed"],
    "pagination": {
      "per_page": 50
    }
  }
}
```

### 2. Analyze Conversation Quality

#### Basic analysis
```json
{
  "tool": "analyze-conversation-quality",
  "arguments": {
    "conversationId": "123456789",
    "analysisDepth": "basic"
  }
}
```

#### Comprehensive analysis with focus areas
```json
{
  "tool": "analyze-conversation-quality",
  "arguments": {
    "conversationId": "123456789",
    "analysisDepth": "comprehensive",
    "focusAreas": ["responsiveness", "professionalism"],
    "includeConversationParts": true,
    "includeCustomerFeedback": true
  }
}
```

### 3. Get Unrated Conversations

#### Find recent unrated conversations
```json
{
  "tool": "get-unrated-conversations",
  "arguments": {
    "dateRange": {
      "startDate": "2024-01-01T00:00:00Z",
      "endDate": "2024-01-31T23:59:59Z"
    },
    "status": ["closed"],
    "pagination": {
      "per_page": 25
    }
  }
}
```

#### Filter by specific support agents
```json
{
  "tool": "get-unrated-conversations",
  "arguments": {
    "adminIds": ["admin_123", "admin_456"],
    "priority": ["high", "normal"]
  }
}
```

### 4. Analyze Support Performance

#### Monthly performance review
```json
{
  "tool": "analyze-support-performance",
  "arguments": {
    "adminId": "admin_123",
    "dateRange": {
      "startDate": "2024-01-01T00:00:00Z",
      "endDate": "2024-01-31T23:59:59Z"
    },
    "includeConversationExamples": true,
    "maxExamples": 5
  }
}
```

### 5. Get Conversation Insights

#### Full insights analysis
```json
{
  "tool": "get-conversation-insights",
  "arguments": {
    "conversationId": "123456789",
    "includeTopics": true,
    "includeSentiment": true,
    "includeActionItems": true
  }
}
```

#### Sentiment analysis only
```json
{
  "tool": "get-conversation-insights",
  "arguments": {
    "conversationId": "123456789",
    "includeTopics": false,
    "includeSentiment": true,
    "includeActionItems": false
  }
}
```

### 6. Bulk Analyze Conversations

#### Analyze multiple conversations
```json
{
  "tool": "bulk-analyze-conversations",
  "arguments": {
    "conversationIds": ["123456789", "987654321", "456789123"],
    "analysisDepth": "detailed",
    "generateSummary": true
  }
}
```

#### Focus on specific quality dimensions
```json
{
  "tool": "bulk-analyze-conversations",
  "arguments": {
    "conversationIds": ["123456789", "987654321"],
    "analysisDepth": "comprehensive",
    "focusAreas": ["helpfulness", "resolution"],
    "includeConversationParts": true,
    "generateSummary": true
  }
}
```

## Common Workflows

### Weekly Quality Review
1. Get unrated conversations from the past week
2. Analyze quality of a sample of conversations
3. Generate performance reports for each support agent
4. Identify trends and improvement opportunities

### Customer Satisfaction Analysis
1. List conversations with ratings
2. Analyze conversations with low ratings
3. Extract insights to understand common issues
4. Generate recommendations for improvement

### Support Team Performance
1. Get conversations by admin for a specific period
2. Analyze support performance for each team member
3. Compare metrics across team members
4. Identify training needs and best practices

## Response Examples

### Quality Analysis Response
```json
{
  "conversationId": "123456789",
  "overallScore": 8.5,
  "dimensions": {
    "responsiveness": {
      "score": 9,
      "reasoning": "Quick initial response and follow-ups",
      "examples": ["Responded within 2 minutes"]
    },
    "helpfulness": {
      "score": 8,
      "reasoning": "Provided clear solutions",
      "examples": ["Offered step-by-step guidance"]
    }
  },
  "summary": "High-quality interaction with excellent responsiveness",
  "recommendations": ["Consider providing more detailed explanations"],
  "strengths": ["Quick response time", "Professional tone"],
  "weaknesses": ["Could be more thorough in explanations"]
}
```

### Performance Analysis Response
```json
{
  "adminId": "admin_123",
  "adminName": "John Doe",
  "metrics": {
    "totalConversations": 45,
    "averageResponseTime": 12.5,
    "customerSatisfactionScore": 4.2,
    "qualityScore": 8.1
  },
  "strengths": ["Excellent response times", "Professional communication"],
  "areasForImprovement": ["Technical knowledge", "Follow-up consistency"],
  "recommendations": ["Additional technical training", "Implement follow-up reminders"]
}
```

## Error Handling

The server provides detailed error messages for common issues:

- **Authentication errors**: Check your Intercom access token
- **Rate limiting**: The server automatically handles rate limits
- **Invalid conversation IDs**: Verify the conversation exists and is accessible
- **OpenAI API errors**: Check your API key and usage limits

## Best Practices

1. **Batch Processing**: Use bulk analysis for multiple conversations
2. **Rate Limiting**: The server respects API limits automatically
3. **Error Recovery**: Check error messages for specific guidance
4. **Data Privacy**: Sensitive information is automatically sanitized
5. **Performance**: Use appropriate analysis depth for your needs
