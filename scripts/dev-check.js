#!/usr/bin/env node

/**
 * Development check script to validate the server can start
 */

const { spawn } = require('child_process');
const fs = require('fs');

console.log('🔍 Running development checks...\n');

// Check if .env exists
if (!fs.existsSync('.env')) {
  console.log('⚠️  Creating .env from .env.example...');
  if (fs.existsSync('.env.example')) {
    fs.copyFileSync('.env.example', '.env');
    console.log('✅ .env file created. Please configure your API keys.');
  } else {
    console.error('❌ .env.example not found');
    process.exit(1);
  }
}

// Function to run a command and return a promise
function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`🔧 Running: ${command} ${args.join(' ')}`);
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

async function runChecks() {
  try {
    // Check TypeScript compilation
    console.log('\n📝 Checking TypeScript compilation...');
    await runCommand('npx', ['tsc', '--noEmit']);
    console.log('✅ TypeScript compilation successful');

    // Check linting
    console.log('\n🔍 Running ESLint...');
    try {
      await runCommand('npx', ['eslint', 'src/**/*.ts']);
      console.log('✅ ESLint passed');
    } catch (error) {
      console.log('⚠️  ESLint found issues (non-blocking)');
    }

    // Try to build
    console.log('\n🏗️  Building project...');
    await runCommand('npm', ['run', 'build']);
    console.log('✅ Build successful');

    // Check if dist directory was created
    if (fs.existsSync('dist')) {
      console.log('✅ dist directory created');
      
      // List generated files
      const files = fs.readdirSync('dist', { recursive: true });
      console.log(`📁 Generated ${files.length} files in dist/`);
    }

    console.log('\n🎉 All development checks passed!');
    console.log('\n📚 Next steps:');
    console.log('  1. Configure your API keys in .env');
    console.log('  2. Run "npm start" to start the server');
    console.log('  3. Test with MCP client or inspector');

  } catch (error) {
    console.error('\n❌ Development check failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('  1. Make sure all dependencies are installed: npm install');
    console.log('  2. Check TypeScript errors and fix them');
    console.log('  3. Verify your tsconfig.json is correct');
    console.log('  4. Check the error message above for specific issues');
    process.exit(1);
  }
}

runChecks();
