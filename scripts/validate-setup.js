#!/usr/bin/env node

/**
 * Validation script to check if the MCP server setup is correct
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Validating Intercom MCP Server setup...\n');

// Check if we're in the right directory
const packageJsonPath = path.join(process.cwd(), 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ package.json not found. Please run this script from the project root.');
  process.exit(1);
}

const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
console.log(`✅ Project: ${packageJson.name} v${packageJson.version}`);

// Check required files
const requiredFiles = [
  'src/server.ts',
  'src/services/intercom.ts',
  'src/services/openai.ts',
  'src/services/analysis.ts',
  'src/utils/config.ts',
  'src/utils/logger.ts',
  'src/utils/validation.ts',
  'src/types/intercom.ts',
  'src/types/analysis.ts',
  'src/types/index.ts',
  'tsconfig.json',
  '.env.example',
  'README.md'
];

console.log('\n📁 Checking required files:');
let missingFiles = [];

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`  ✅ ${file}`);
  } else {
    console.log(`  ❌ ${file}`);
    missingFiles.push(file);
  }
});

if (missingFiles.length > 0) {
  console.error(`\n❌ Missing ${missingFiles.length} required files. Please ensure all files are present.`);
  process.exit(1);
}

// Check dependencies
console.log('\n📦 Checking dependencies:');
const requiredDeps = [
  '@modelcontextprotocol/sdk',
  'openai',
  'axios',
  'zod',
  'dotenv',
  'winston'
];

const devDeps = [
  'typescript',
  '@types/node',
  'tsx'
];

requiredDeps.forEach(dep => {
  if (packageJson.dependencies && packageJson.dependencies[dep]) {
    console.log(`  ✅ ${dep} v${packageJson.dependencies[dep]}`);
  } else {
    console.log(`  ❌ ${dep} (missing)`);
  }
});

devDeps.forEach(dep => {
  if (packageJson.devDependencies && packageJson.devDependencies[dep]) {
    console.log(`  ✅ ${dep} v${packageJson.devDependencies[dep]} (dev)`);
  } else {
    console.log(`  ❌ ${dep} (missing dev dependency)`);
  }
});

// Check environment setup
console.log('\n🔧 Environment setup:');
if (fs.existsSync('.env')) {
  console.log('  ✅ .env file exists');
  
  // Check if .env has required variables (without reading sensitive data)
  const envContent = fs.readFileSync('.env', 'utf8');
  const hasIntercomToken = envContent.includes('INTERCOM_ACCESS_TOKEN=');
  const hasOpenAIKey = envContent.includes('OPENAI_API_KEY=');
  
  if (hasIntercomToken) {
    console.log('  ✅ INTERCOM_ACCESS_TOKEN configured');
  } else {
    console.log('  ⚠️  INTERCOM_ACCESS_TOKEN not found in .env');
  }
  
  if (hasOpenAIKey) {
    console.log('  ✅ OPENAI_API_KEY configured');
  } else {
    console.log('  ⚠️  OPENAI_API_KEY not found in .env');
  }
} else {
  console.log('  ⚠️  .env file not found (copy from .env.example)');
}

// Check TypeScript configuration
console.log('\n⚙️  TypeScript configuration:');
try {
  const tsConfig = JSON.parse(fs.readFileSync('tsconfig.json', 'utf8'));
  console.log(`  ✅ Target: ${tsConfig.compilerOptions.target}`);
  console.log(`  ✅ Module: ${tsConfig.compilerOptions.module}`);
  console.log(`  ✅ Output: ${tsConfig.compilerOptions.outDir || 'dist'}`);
} catch (error) {
  console.log('  ❌ Invalid tsconfig.json');
}

// Check scripts
console.log('\n🚀 Available scripts:');
if (packageJson.scripts) {
  Object.entries(packageJson.scripts).forEach(([name, script]) => {
    console.log(`  ✅ npm run ${name}: ${script}`);
  });
} else {
  console.log('  ❌ No scripts defined');
}

// Summary
console.log('\n📋 Setup Summary:');
console.log('  1. Install dependencies: npm install');
console.log('  2. Copy environment file: cp .env.example .env');
console.log('  3. Configure your API keys in .env');
console.log('  4. Build the project: npm run build');
console.log('  5. Start the server: npm start');

console.log('\n✨ Validation complete! Your Intercom MCP Server setup looks good.');
console.log('\n📚 Next steps:');
console.log('  - Configure your .env file with valid API keys');
console.log('  - Run "npm run build" to compile TypeScript');
console.log('  - Run "npm start" to start the MCP server');
console.log('  - Check examples/usage.md for usage examples');
