/**
 * Tests for configuration management
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';

describe('Configuration', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    // Reset environment variables
    process.env = { ...originalEnv };
  });

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv;
  });

  it('should validate required environment variables', () => {
    // Set required environment variables
    process.env['INTERCOM_ACCESS_TOKEN'] = 'test_token';
    process.env['OPENAI_API_KEY'] = 'test_key';

    // Import config after setting env vars
    const { validateEnvironment } = require('../utils/config.js');
    
    expect(() => validateEnvironment()).not.toThrow();
  });

  it('should throw error for missing required environment variables', () => {
    // Clear required environment variables
    delete process.env['INTERCOM_ACCESS_TOKEN'];
    delete process.env['OPENAI_API_KEY'];

    // Import config after clearing env vars
    const { validateEnvironment } = require('../utils/config.js');
    
    expect(() => validateEnvironment()).toThrow('Missing required environment variables');
  });

  it('should use default values for optional configuration', () => {
    // Set required environment variables
    process.env['INTERCOM_ACCESS_TOKEN'] = 'test_token';
    process.env['OPENAI_API_KEY'] = 'test_key';

    // Import config after setting env vars
    const { getConfig } = require('../utils/config.js');
    
    const config = getConfig();
    
    expect(config.intercom.apiVersion).toBe('2.13');
    expect(config.openai.model).toBe('gpt-4o-mini');
    expect(config.server.logLevel).toBe('info');
    expect(config.analysis.defaultAnalysisDepth).toBe('detailed');
  });
});
