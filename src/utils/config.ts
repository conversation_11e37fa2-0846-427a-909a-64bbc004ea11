/**
 * Configuration management with validation and type safety
 */

import { z } from 'zod';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Configuration schema with validation
const configSchema = z.object({
  // Intercom configuration
  intercom: z.object({
    accessToken: z.string().min(1, 'Intercom access token is required'),
    apiVersion: z.string().default('2.13'),
    baseUrl: z.string().url().default('https://api.intercom.io'),
    rateLimitRequestsPerMinute: z.number().min(1).max(1000).default(60),
  }),

  // OpenAI configuration
  openai: z.object({
    apiKey: z.string().min(1, 'OpenAI API key is required'),
    model: z.string().default('gpt-4o-mini'),
    maxTokens: z.number().min(100).max(4000).default(2000),
    temperature: z.number().min(0).max(2).default(0.3),
  }),

  // Server configuration
  server: z.object({
    logLevel: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
    nodeEnv: z.enum(['development', 'production', 'test']).default('development'),
    cacheTtlSeconds: z.number().min(60).max(3600).default(300),
  }),

  // Analysis configuration
  analysis: z.object({
    maxConcurrentAnalyses: z.number().min(1).max(10).default(3),
    defaultAnalysisDepth: z.enum(['basic', 'detailed', 'comprehensive']).default('detailed'),
    includeConversationParts: z.boolean().default(true),
    includeCustomerFeedback: z.boolean().default(true),
  }),
});

export type Config = z.infer<typeof configSchema>;

/**
 * Load and validate configuration from environment variables
 */
function loadConfig(): Config {
  const rawConfig = {
    intercom: {
      accessToken: process.env['INTERCOM_ACCESS_TOKEN'] || '',
      apiVersion: process.env['INTERCOM_API_VERSION'] || '2.13',
      baseUrl: process.env['INTERCOM_BASE_URL'] || 'https://api.intercom.io',
      rateLimitRequestsPerMinute: parseInt(process.env['RATE_LIMIT_REQUESTS_PER_MINUTE'] || '60', 10),
    },
    openai: {
      apiKey: process.env['OPENAI_API_KEY'] || '',
      model: process.env['OPENAI_MODEL'] || 'gpt-4o-mini',
      maxTokens: parseInt(process.env['OPENAI_MAX_TOKENS'] || '2000', 10),
      temperature: parseFloat(process.env['OPENAI_TEMPERATURE'] || '0.3'),
    },
    server: {
      logLevel: (process.env['LOG_LEVEL'] || 'info') as 'error' | 'warn' | 'info' | 'debug',
      nodeEnv: (process.env['NODE_ENV'] || 'development') as 'development' | 'production' | 'test',
      cacheTtlSeconds: parseInt(process.env['CACHE_TTL_SECONDS'] || '300', 10),
    },
    analysis: {
      maxConcurrentAnalyses: parseInt(process.env['MAX_CONCURRENT_ANALYSES'] || '3', 10),
      defaultAnalysisDepth: (process.env['DEFAULT_ANALYSIS_DEPTH'] || 'detailed') as 'basic' | 'detailed' | 'comprehensive',
      includeConversationParts: process.env['INCLUDE_CONVERSATION_PARTS'] !== 'false',
      includeCustomerFeedback: process.env['INCLUDE_CUSTOMER_FEEDBACK'] !== 'false',
    },
  };

  try {
    return configSchema.parse(rawConfig);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
      throw new Error(`Configuration validation failed:\n${errorMessages.join('\n')}`);
    }
    throw error;
  }
}

/**
 * Validate that all required environment variables are set
 */
export function validateEnvironment(): void {
  const requiredVars = [
    'INTERCOM_ACCESS_TOKEN',
    'OPENAI_API_KEY',
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missingVars.join(', ')}\n` +
      'Please check your .env file or environment configuration.'
    );
  }
}

/**
 * Get the validated configuration
 */
export function getConfig(): Config {
  validateEnvironment();
  return loadConfig();
}

/**
 * Check if running in development mode
 */
export function isDevelopment(): boolean {
  return getConfig().server.nodeEnv === 'development';
}

/**
 * Check if running in production mode
 */
export function isProduction(): boolean {
  return getConfig().server.nodeEnv === 'production';
}

/**
 * Check if running in test mode
 */
export function isTest(): boolean {
  return getConfig().server.nodeEnv === 'test';
}

// Export the singleton config instance
export const config = getConfig();
