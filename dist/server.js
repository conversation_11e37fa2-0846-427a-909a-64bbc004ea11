#!/usr/bin/env node
/**
 * Intercom MCP Server
 *
 * A Model Context Protocol server for analyzing Intercom conversations
 * and support quality using OpenAI.
 */
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { z } from 'zod';
import { AnalysisService } from './services/analysis.js';
import { config } from './utils/config.js';
import { log } from './utils/logger.js';
import { validateInput, listConversationsSchema, analyzeConversationSchema, getUnratedConversationsSchema, analyzeSupportPerformanceSchema, getConversationInsightsSchema, bulkAnalyzeConversationsSchema, } from './utils/validation.js';
class IntercomMcpServer {
    server;
    analysisService;
    constructor() {
        this.server = new McpServer({
            name: 'intercom-analysis-server',
            version: '1.0.0',
        });
        this.analysisService = new AnalysisService();
        this.setupTools();
    }
    setupTools() {
        // Tool 1: List conversations with filtering
        this.server.tool('list-conversations', {
            dateRange: z.object({
                startDate: z.string().datetime(),
                endDate: z.string().datetime(),
            }).optional(),
            adminIds: z.array(z.string()).optional(),
            tags: z.array(z.string()).optional(),
            status: z.array(z.enum(['open', 'closed', 'snoozed'])).optional(),
            hasRating: z.boolean().optional(),
            pagination: z.object({
                page: z.number().int().min(1).optional(),
                per_page: z.number().int().min(1).max(150).optional(),
                starting_after: z.string().optional(),
            }).optional(),
        }, async (params) => {
            try {
                const validatedParams = validateInput(listConversationsSchema, params);
                const result = await this.analysisService.listConversations({
                    dateRange: validatedParams.dateRange || undefined,
                    adminIds: validatedParams.adminIds || undefined,
                    tags: validatedParams.tags || undefined,
                    status: validatedParams.status || undefined,
                    hasRating: validatedParams.hasRating || undefined,
                }, validatedParams.pagination || {});
                return {
                    content: [{
                            type: 'text',
                            text: JSON.stringify({
                                conversations: result.conversations.map(conv => ({
                                    id: conv.id,
                                    title: conv.title,
                                    status: conv.state,
                                    priority: conv.priority,
                                    createdAt: new Date(conv.created_at * 1000).toISOString(),
                                    updatedAt: new Date(conv.updated_at * 1000).toISOString(),
                                    hasRating: !!conv.conversation_rating,
                                    rating: conv.conversation_rating?.rating,
                                    adminAssigneeId: conv.admin_assignee_id,
                                    messageCount: conv.conversation_parts?.total_count || 0,
                                    tags: conv.tags.tags.map(tag => tag.name),
                                })),
                                totalCount: result.totalCount,
                            }, null, 2),
                        }],
                };
            }
            catch (error) {
                log.error('list-conversations tool failed', {}, error);
                return {
                    content: [{
                            type: 'text',
                            text: `Error: ${error.message}`,
                        }],
                    isError: true,
                };
            }
        });
        // Tool 2: Analyze conversation quality
        this.server.tool('analyze-conversation-quality', {
            conversationId: z.string().min(1),
            includeConversationParts: z.boolean().default(true),
            includeCustomerFeedback: z.boolean().default(true),
            analysisDepth: z.enum(['basic', 'detailed', 'comprehensive']).default('detailed'),
            focusAreas: z.array(z.enum(['responsiveness', 'helpfulness', 'professionalism', 'resolution'])).optional(),
        }, async (params) => {
            try {
                const validatedParams = validateInput(analyzeConversationSchema, params);
                const analysis = await this.analysisService.analyzeConversationQuality(validatedParams.conversationId, {
                    includeConversationParts: validatedParams.includeConversationParts ?? true,
                    includeCustomerFeedback: validatedParams.includeCustomerFeedback ?? true,
                    analysisDepth: validatedParams.analysisDepth ?? 'detailed',
                    focusAreas: validatedParams.focusAreas || undefined,
                });
                return {
                    content: [{
                            type: 'text',
                            text: JSON.stringify(analysis, null, 2),
                        }],
                };
            }
            catch (error) {
                log.error('analyze-conversation-quality tool failed', { conversationId: params.conversationId }, error);
                return {
                    content: [{
                            type: 'text',
                            text: `Error analyzing conversation: ${error.message}`,
                        }],
                    isError: true,
                };
            }
        });
        // Tool 3: Get unrated conversations
        this.server.tool('get-unrated-conversations', {
            dateRange: z.object({
                startDate: z.string().datetime(),
                endDate: z.string().datetime(),
            }).optional(),
            adminIds: z.array(z.string()).optional(),
            status: z.array(z.enum(['open', 'closed', 'snoozed'])).optional(),
            priority: z.array(z.enum(['high', 'normal', 'low'])).optional(),
            pagination: z.object({
                page: z.number().int().min(1).optional(),
                per_page: z.number().int().min(1).max(150).optional(),
                starting_after: z.string().optional(),
            }).optional(),
            includePredictedSatisfaction: z.boolean().default(false),
        }, async (params) => {
            try {
                const validatedParams = validateInput(getUnratedConversationsSchema, params);
                const unratedConversations = await this.analysisService.getUnratedConversations({
                    dateRange: validatedParams.dateRange || undefined,
                    adminIds: validatedParams.adminIds || undefined,
                    status: validatedParams.status || undefined,
                    priority: validatedParams.priority || undefined,
                }, validatedParams.pagination || {});
                return {
                    content: [{
                            type: 'text',
                            text: JSON.stringify({
                                unratedConversations,
                                count: unratedConversations.length,
                                summary: {
                                    byStatus: this.groupBy(unratedConversations, 'status'),
                                    byPriority: this.groupBy(unratedConversations, 'priority'),
                                    averageMessageCount: unratedConversations.reduce((sum, conv) => sum + conv.messageCount, 0) / unratedConversations.length || 0,
                                },
                            }, null, 2),
                        }],
                };
            }
            catch (error) {
                log.error('get-unrated-conversations tool failed', {}, error);
                return {
                    content: [{
                            type: 'text',
                            text: `Error fetching unrated conversations: ${error.message}`,
                        }],
                    isError: true,
                };
            }
        });
        // Tool 4: Analyze support performance
        this.server.tool('analyze-support-performance', {
            adminId: z.string().min(1),
            dateRange: z.object({
                startDate: z.string().datetime(),
                endDate: z.string().datetime(),
            }),
            includeConversationExamples: z.boolean().default(true),
            maxExamples: z.number().int().min(1).max(10).default(5),
        }, async (params) => {
            try {
                const validatedParams = validateInput(analyzeSupportPerformanceSchema, params);
                const performance = await this.analysisService.analyzeSupportPerformance(validatedParams.adminId, validatedParams.dateRange, {
                    includeConversationExamples: validatedParams.includeConversationExamples ?? true,
                    maxExamples: validatedParams.maxExamples ?? 5,
                });
                return {
                    content: [{
                            type: 'text',
                            text: JSON.stringify(performance, null, 2),
                        }],
                };
            }
            catch (error) {
                log.error('analyze-support-performance tool failed', { adminId: params.adminId }, error);
                return {
                    content: [{
                            type: 'text',
                            text: `Error analyzing support performance: ${error.message}`,
                        }],
                    isError: true,
                };
            }
        });
        // Tool 5: Get conversation insights
        this.server.tool('get-conversation-insights', {
            conversationId: z.string().min(1),
            includeTopics: z.boolean().default(true),
            includeSentiment: z.boolean().default(true),
            includeActionItems: z.boolean().default(true),
        }, async (params) => {
            try {
                const validatedParams = validateInput(getConversationInsightsSchema, params);
                const insights = await this.analysisService.getConversationInsights(validatedParams.conversationId, {
                    includeTopics: validatedParams.includeTopics ?? true,
                    includeSentiment: validatedParams.includeSentiment ?? true,
                    includeActionItems: validatedParams.includeActionItems ?? true,
                });
                return {
                    content: [{
                            type: 'text',
                            text: JSON.stringify(insights, null, 2),
                        }],
                };
            }
            catch (error) {
                log.error('get-conversation-insights tool failed', { conversationId: params.conversationId }, error);
                return {
                    content: [{
                            type: 'text',
                            text: `Error getting conversation insights: ${error.message}`,
                        }],
                    isError: true,
                };
            }
        });
        // Tool 6: Bulk analyze conversations
        this.server.tool('bulk-analyze-conversations', {
            conversationIds: z.array(z.string().min(1)).min(1).max(50),
            analysisDepth: z.enum(['basic', 'detailed', 'comprehensive']).default('detailed'),
            includeConversationParts: z.boolean().default(true),
            includeCustomerFeedback: z.boolean().default(true),
            focusAreas: z.array(z.enum(['responsiveness', 'helpfulness', 'professionalism', 'resolution'])).optional(),
            generateSummary: z.boolean().default(true),
        }, async (params) => {
            try {
                const validatedParams = validateInput(bulkAnalyzeConversationsSchema, params);
                const bulkResult = await this.analysisService.bulkAnalyzeConversations(validatedParams.conversationIds, {
                    includeConversationParts: validatedParams.includeConversationParts ?? true,
                    includeCustomerFeedback: validatedParams.includeCustomerFeedback ?? true,
                    analysisDepth: validatedParams.analysisDepth ?? 'detailed',
                    focusAreas: validatedParams.focusAreas || undefined,
                });
                return {
                    content: [{
                            type: 'text',
                            text: JSON.stringify(bulkResult, null, 2),
                        }],
                };
            }
            catch (error) {
                log.error('bulk-analyze-conversations tool failed', { conversationIds: params.conversationIds }, error);
                return {
                    content: [{
                            type: 'text',
                            text: `Error performing bulk analysis: ${error.message}`,
                        }],
                    isError: true,
                };
            }
        });
    }
    groupBy(array, key) {
        return array.reduce((groups, item) => {
            const value = String(item[key]);
            groups[value] = (groups[value] || 0) + 1;
            return groups;
        }, {});
    }
    async start() {
        try {
            log.info('Starting Intercom MCP Server', {
                version: '1.0.0',
                nodeEnv: config.server.nodeEnv,
                logLevel: config.server.logLevel,
            });
            const transport = new StdioServerTransport();
            await this.server.connect(transport);
            log.info('Intercom MCP Server started successfully');
        }
        catch (error) {
            log.error('Failed to start Intercom MCP Server', {}, error);
            process.exit(1);
        }
    }
}
// Start the server if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
    const server = new IntercomMcpServer();
    server.start().catch((error) => {
        console.error('Failed to start server:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=server.js.map