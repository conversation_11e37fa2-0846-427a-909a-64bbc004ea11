/**
 * Configuration management with validation and type safety
 */
import { z } from 'zod';
declare const configSchema: z.ZodObject<{
    intercom: z.ZodObject<{
        accessToken: z.ZodString;
        apiVersion: z.ZodDefault<z.ZodString>;
        baseUrl: z.Zod<PERSON>efault<z.ZodString>;
        rateLimitRequestsPerMinute: z.Z<PERSON>efault<z.ZodNumber>;
    }, "strip", z.ZodType<PERSON>ny, {
        accessToken: string;
        apiVersion: string;
        baseUrl: string;
        rateLimitRequestsPerMinute: number;
    }, {
        accessToken: string;
        apiVersion?: string | undefined;
        baseUrl?: string | undefined;
        rateLimitRequestsPerMinute?: number | undefined;
    }>;
    openai: z.ZodObject<{
        apiKey: z.ZodString;
        model: z.ZodDefault<z.ZodString>;
        maxTokens: z.ZodDefault<z.ZodNumber>;
        temperature: z.ZodDefault<z.<PERSON>od<PERSON>>;
    }, "strip", z.<PERSON><PERSON><PERSON><PERSON>ny, {
        apiKey: string;
        model: string;
        maxTokens: number;
        temperature: number;
    }, {
        apiKey: string;
        model?: string | undefined;
        maxTokens?: number | undefined;
        temperature?: number | undefined;
    }>;
    server: z.ZodObject<{
        logLevel: z.ZodDefault<z.ZodEnum<["error", "warn", "info", "debug"]>>;
        nodeEnv: z.ZodDefault<z.ZodEnum<["development", "production", "test"]>>;
        cacheTtlSeconds: z.ZodDefault<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        logLevel: "error" | "warn" | "info" | "debug";
        nodeEnv: "development" | "production" | "test";
        cacheTtlSeconds: number;
    }, {
        logLevel?: "error" | "warn" | "info" | "debug" | undefined;
        nodeEnv?: "development" | "production" | "test" | undefined;
        cacheTtlSeconds?: number | undefined;
    }>;
    analysis: z.ZodObject<{
        maxConcurrentAnalyses: z.ZodDefault<z.ZodNumber>;
        defaultAnalysisDepth: z.ZodDefault<z.ZodEnum<["basic", "detailed", "comprehensive"]>>;
        includeConversationParts: z.ZodDefault<z.ZodBoolean>;
        includeCustomerFeedback: z.ZodDefault<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        maxConcurrentAnalyses: number;
        defaultAnalysisDepth: "basic" | "detailed" | "comprehensive";
        includeConversationParts: boolean;
        includeCustomerFeedback: boolean;
    }, {
        maxConcurrentAnalyses?: number | undefined;
        defaultAnalysisDepth?: "basic" | "detailed" | "comprehensive" | undefined;
        includeConversationParts?: boolean | undefined;
        includeCustomerFeedback?: boolean | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    intercom: {
        accessToken: string;
        apiVersion: string;
        baseUrl: string;
        rateLimitRequestsPerMinute: number;
    };
    openai: {
        apiKey: string;
        model: string;
        maxTokens: number;
        temperature: number;
    };
    server: {
        logLevel: "error" | "warn" | "info" | "debug";
        nodeEnv: "development" | "production" | "test";
        cacheTtlSeconds: number;
    };
    analysis: {
        maxConcurrentAnalyses: number;
        defaultAnalysisDepth: "basic" | "detailed" | "comprehensive";
        includeConversationParts: boolean;
        includeCustomerFeedback: boolean;
    };
}, {
    intercom: {
        accessToken: string;
        apiVersion?: string | undefined;
        baseUrl?: string | undefined;
        rateLimitRequestsPerMinute?: number | undefined;
    };
    openai: {
        apiKey: string;
        model?: string | undefined;
        maxTokens?: number | undefined;
        temperature?: number | undefined;
    };
    server: {
        logLevel?: "error" | "warn" | "info" | "debug" | undefined;
        nodeEnv?: "development" | "production" | "test" | undefined;
        cacheTtlSeconds?: number | undefined;
    };
    analysis: {
        maxConcurrentAnalyses?: number | undefined;
        defaultAnalysisDepth?: "basic" | "detailed" | "comprehensive" | undefined;
        includeConversationParts?: boolean | undefined;
        includeCustomerFeedback?: boolean | undefined;
    };
}>;
export type Config = z.infer<typeof configSchema>;
/**
 * Validate that all required environment variables are set
 */
export declare function validateEnvironment(): void;
/**
 * Get the validated configuration
 */
export declare function getConfig(): Config;
/**
 * Check if running in development mode
 */
export declare function isDevelopment(): boolean;
/**
 * Check if running in production mode
 */
export declare function isProduction(): boolean;
/**
 * Check if running in test mode
 */
export declare function isTest(): boolean;
export declare const config: {
    intercom: {
        accessToken: string;
        apiVersion: string;
        baseUrl: string;
        rateLimitRequestsPerMinute: number;
    };
    openai: {
        apiKey: string;
        model: string;
        maxTokens: number;
        temperature: number;
    };
    server: {
        logLevel: "error" | "warn" | "info" | "debug";
        nodeEnv: "development" | "production" | "test";
        cacheTtlSeconds: number;
    };
    analysis: {
        maxConcurrentAnalyses: number;
        defaultAnalysisDepth: "basic" | "detailed" | "comprehensive";
        includeConversationParts: boolean;
        includeCustomerFeedback: boolean;
    };
};
export {};
//# sourceMappingURL=config.d.ts.map