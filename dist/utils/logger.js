/**
 * Centralized logging utility with structured logging support
 */
import winston from 'winston';
import { config } from './config.js';
// Define log levels
const logLevels = {
    error: 0,
    warn: 1,
    info: 2,
    debug: 3,
};
// Custom format for structured logging
const logFormat = winston.format.combine(winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss.SSS',
}), winston.format.errors({ stack: true }), winston.format.json(), winston.format.printf(({ timestamp, level, message, ...meta }) => {
    const logEntry = {
        timestamp,
        level,
        message,
        ...meta,
    };
    return JSON.stringify(logEntry);
}));
// Create the logger instance
const logger = winston.createLogger({
    levels: logLevels,
    level: config.server.logLevel,
    format: logFormat,
    defaultMeta: {
        service: 'intercom-mcp-server',
        version: '1.0.0',
    },
    transports: [
        // Console transport for development
        new winston.transports.Console({
            format: winston.format.combine(winston.format.colorize(), winston.format.simple()),
        }),
    ],
});
// Add file transport for production
if (config.server.nodeEnv === 'production') {
    logger.add(new winston.transports.File({
        filename: 'logs/error.log',
        level: 'error',
        maxsize: 5242880, // 5MB
        maxFiles: 5,
    }));
    logger.add(new winston.transports.File({
        filename: 'logs/combined.log',
        maxsize: 5242880, // 5MB
        maxFiles: 5,
    }));
}
/**
 * Enhanced logger with context support
 */
class ContextualLogger {
    baseContext;
    constructor(baseContext = {}) {
        this.baseContext = baseContext;
    }
    /**
     * Create a child logger with additional context
     */
    child(context) {
        return new ContextualLogger({ ...this.baseContext, ...context });
    }
    /**
     * Log an error message
     */
    error(message, context = {}, error) {
        const logContext = { ...this.baseContext, ...context };
        if (error) {
            logContext.error = {
                name: error.name,
                message: error.message,
                stack: error.stack,
            };
        }
        logger.error(message, logContext);
    }
    /**
     * Log a warning message
     */
    warn(message, context = {}) {
        logger.warn(message, { ...this.baseContext, ...context });
    }
    /**
     * Log an info message
     */
    info(message, context = {}) {
        logger.info(message, { ...this.baseContext, ...context });
    }
    /**
     * Log a debug message
     */
    debug(message, context = {}) {
        logger.debug(message, { ...this.baseContext, ...context });
    }
    /**
     * Log API request details
     */
    apiRequest(method, url, context = {}) {
        this.info('API request', {
            ...context,
            api: {
                method,
                url,
            },
        });
    }
    /**
     * Log API response details
     */
    apiResponse(method, url, statusCode, duration, context = {}) {
        this.info('API response', {
            ...context,
            api: {
                method,
                url,
                statusCode,
                duration,
            },
        });
    }
    /**
     * Log analysis operation
     */
    analysis(operation, conversationId, context = {}) {
        this.info('Analysis operation', {
            ...context,
            operation,
            conversationId,
        });
    }
    /**
     * Log performance metrics
     */
    performance(operation, duration, context = {}) {
        this.info('Performance metric', {
            ...context,
            performance: {
                operation,
                duration,
            },
        });
    }
    /**
     * Log rate limiting information
     */
    rateLimit(service, remaining, resetTime, context = {}) {
        this.info('Rate limit status', {
            ...context,
            rateLimit: {
                service,
                remaining,
                resetTime,
            },
        });
    }
    /**
     * Log cache operations
     */
    cache(operation, key, context = {}) {
        this.debug('Cache operation', {
            ...context,
            cache: {
                operation,
                key,
            },
        });
    }
}
// Export the default logger instance
export const log = new ContextualLogger();
// Export the logger class for creating contextual loggers
export { ContextualLogger };
// Export utility functions
export function createLogger(context) {
    return new ContextualLogger(context);
}
export function logError(error, context = {}) {
    log.error('Unhandled error', context, error);
}
export function logApiError(operation, error, context = {}) {
    log.error(`API error during ${operation}`, {
        ...context,
        operation,
    }, error);
}
//# sourceMappingURL=logger.js.map