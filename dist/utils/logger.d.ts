/**
 * Centralized logging utility with structured logging support
 */
import type { LogContext } from '../types/index.js';
/**
 * Enhanced logger with context support
 */
declare class ContextualLogger {
    private baseContext;
    constructor(baseContext?: LogContext);
    /**
     * Create a child logger with additional context
     */
    child(context: LogContext): ContextualLogger;
    /**
     * Log an error message
     */
    error(message: string, context?: LogContext, error?: Error): void;
    /**
     * Log a warning message
     */
    warn(message: string, context?: LogContext): void;
    /**
     * Log an info message
     */
    info(message: string, context?: LogContext): void;
    /**
     * Log a debug message
     */
    debug(message: string, context?: LogContext): void;
    /**
     * Log API request details
     */
    apiRequest(method: string, url: string, context?: LogContext): void;
    /**
     * Log API response details
     */
    apiResponse(method: string, url: string, statusCode: number, duration: number, context?: LogContext): void;
    /**
     * Log analysis operation
     */
    analysis(operation: string, conversationId: string, context?: LogContext): void;
    /**
     * Log performance metrics
     */
    performance(operation: string, duration: number, context?: LogContext): void;
    /**
     * Log rate limiting information
     */
    rateLimit(service: string, remaining: number, resetTime: number, context?: LogContext): void;
    /**
     * Log cache operations
     */
    cache(operation: 'hit' | 'miss' | 'set' | 'delete', key: string, context?: LogContext): void;
}
export declare const log: ContextualLogger;
export { ContextualLogger };
export declare function createLogger(context: LogContext): ContextualLogger;
export declare function logError(error: Error, context?: LogContext): void;
export declare function logApiError(operation: string, error: Error, context?: LogContext): void;
//# sourceMappingURL=logger.d.ts.map