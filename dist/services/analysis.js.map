{"version": 3, "file": "analysis.js", "sourceRoot": "", "sources": ["../../src/services/analysis.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,iBAAiB,EAAE,MAAM,eAAe,CAAC;AAClD,OAAO,EAAE,qBAAqB,EAAE,MAAM,aAAa,CAAC;AACpD,OAAO,EAAE,GAAG,EAAE,MAAM,oBAAoB,CAAC;AAczC,MAAM,OAAO,eAAe;IAClB,cAAc,CAAoB;IAClC,aAAa,CAAwB;IAE7C;QACE,IAAI,CAAC,cAAc,GAAG,IAAI,iBAAiB,EAAE,CAAC;QAC9C,IAAI,CAAC,aAAa,GAAG,IAAI,qBAAqB,EAAE,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,0BAA0B,CAC9B,cAAsB,EACtB,SAAyB;QACvB,wBAAwB,EAAE,IAAI;QAC9B,uBAAuB,EAAE,IAAI;QAC7B,aAAa,EAAE,UAAU;KAC1B;QAED,IAAI,CAAC;YACH,GAAG,CAAC,IAAI,CAAC,wCAAwC,EAAE,EAAE,cAAc,EAAE,CAAC,CAAC;YAEvE,mCAAmC;YACnC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;YAE/E,sBAAsB;YACtB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,0BAA0B,CAAC,YAAY,EAAE;gBACjF,wBAAwB,EAAE,MAAM,CAAC,wBAAwB;gBACzD,aAAa,EAAE,MAAM,CAAC,aAAa;gBACnC,UAAU,EAAE,MAAM,CAAC,UAAU;aAC9B,CAAC,CAAC;YAEH,GAAG,CAAC,IAAI,CAAC,yCAAyC,EAAE;gBAClD,cAAc;gBACd,YAAY,EAAE,QAAQ,CAAC,YAAY;aACpC,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,GAAG,CAAC,KAAK,CAAC,wCAAwC,EAAE,EAAE,cAAc,EAAE,EAAE,KAAc,CAAC,CAAC;YACxF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAC3B,SAAyB,EAAE,EAC3B,aAA+B,EAAE;QAEjC,IAAI,CAAC;YACH,GAAG,CAAC,IAAI,CAAC,gCAAgC,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC;YAEnE,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,uBAAuB,CAAC;gBACtE,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,UAAU;aACX,CAAC,CAAC;YAEH,MAAM,oBAAoB,GAA0B,aAAa,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC3F,cAAc,EAAE,IAAI,CAAC,EAAE;gBACvB,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,SAAS;gBAC9B,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE;gBACzD,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE;gBACzD,aAAa,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;oBACtC,EAAE,EAAE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE;oBACrC,IAAI,EAAE,SAAS,EAAE,oCAAoC;iBACtD,CAAC,CAAC,CAAC,SAAS;gBACb,YAAY,EAAE;oBACZ,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,SAAS;oBAC9C,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,IAAI,SAAS;oBAClD,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,SAAS;iBACrD;gBACD,MAAM,EAAE,IAAI,CAAC,KAAK;gBAClB,YAAY,EAAE,IAAI,CAAC,kBAAkB,EAAE,WAAW,IAAI,CAAC;gBACvD,YAAY,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE;gBAC5D,QAAQ,EAAE,IAAI,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ;gBAC1D,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC;gBACzC,iBAAiB,EAAE,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC;aACzD,CAAC,CAAC,CAAC;YAEJ,GAAG,CAAC,IAAI,CAAC,+BAA+B,EAAE;gBACxC,KAAK,EAAE,oBAAoB,CAAC,MAAM;gBAClC,UAAU,EAAE,aAAa,CAAC,WAAW;aACtC,CAAC,CAAC;YAEH,OAAO,oBAAoB,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,GAAG,CAAC,KAAK,CAAC,uCAAuC,EAAE,EAAE,MAAM,EAAE,EAAE,KAAc,CAAC,CAAC;YAC/E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,yBAAyB,CAC7B,OAAe,EACf,SAAiD,EACjD,UAGI,EAAE;QAEN,IAAI,CAAC;YACH,GAAG,CAAC,IAAI,CAAC,uCAAuC,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC;YAE1E,sBAAsB;YACtB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAE1D,oCAAoC;YACpC,MAAM,qBAAqB,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,uBAAuB,CAAC,OAAO,EAAE;gBACvF,SAAS;gBACT,UAAU,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,EAAE,wBAAwB;aACxD,CAAC,CAAC;YAEH,kCAAkC;YAClC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,yBAAyB,CACjE,qBAAqB,CAAC,aAAa,EACnC,OAAO,EACP,KAAK,CAAC,IAAI,EACV,SAAS,CACV,CAAC;YAEF,GAAG,CAAC,IAAI,CAAC,wCAAwC,EAAE;gBACjD,OAAO;gBACP,iBAAiB,EAAE,qBAAqB,CAAC,aAAa,CAAC,MAAM;gBAC7D,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,YAAY;aAC5C,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,GAAG,CAAC,KAAK,CAAC,uCAAuC,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,KAAc,CAAC,CAAC;YAC3F,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAC3B,cAAsB,EACtB,UAII,EAAE;QAEN,IAAI,CAAC;YACH,GAAG,CAAC,IAAI,CAAC,+BAA+B,EAAE,EAAE,cAAc,EAAE,OAAO,EAAE,CAAC,CAAC;YAEvE,mCAAmC;YACnC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;YAE/E,2BAA2B;YAC3B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;YAEzF,GAAG,CAAC,IAAI,CAAC,iCAAiC,EAAE;gBAC1C,cAAc;gBACd,WAAW,EAAE,QAAQ,CAAC,MAAM,CAAC,MAAM;gBACnC,gBAAgB,EAAE,QAAQ,CAAC,WAAW,CAAC,MAAM;aAC9C,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,GAAG,CAAC,KAAK,CAAC,qCAAqC,EAAE,EAAE,cAAc,EAAE,EAAE,KAAc,CAAC,CAAC;YACrF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAC5B,eAAyB,EACzB,SAAyB;QACvB,wBAAwB,EAAE,IAAI;QAC9B,uBAAuB,EAAE,IAAI;QAC7B,aAAa,EAAE,UAAU;KAC1B;QAED,IAAI,CAAC;YACH,GAAG,CAAC,IAAI,CAAC,qCAAqC,EAAE;gBAC9C,iBAAiB,EAAE,eAAe,CAAC,MAAM;gBACzC,MAAM;aACP,CAAC,CAAC;YAEH,MAAM,OAAO,GAAkC,EAAE,CAAC;YAClD,MAAM,MAAM,GAAqD,EAAE,CAAC;YAEpE,kEAAkE;YAClE,MAAM,SAAS,GAAG,CAAC,CAAC;YACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;gBAC3D,MAAM,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;gBAEtD,MAAM,aAAa,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,cAAc,EAAE,EAAE;oBACvD,IAAI,CAAC;wBACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;wBAC/E,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBACzB,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,MAAM,CAAC,IAAI,CAAC;4BACV,cAAc;4BACd,KAAK,EAAG,KAAe,CAAC,OAAO;yBAChC,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;gBAEjC,2DAA2D;gBAC3D,IAAI,CAAC,GAAG,SAAS,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC;oBAC3C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;gBAC1D,CAAC;YACH,CAAC;YAED,mBAAmB;YACnB,MAAM,OAAO,GAAG,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,CAAC;YAE1D,MAAM,UAAU,GAAuB;gBACrC,aAAa,EAAE,eAAe,CAAC,MAAM;gBACrC,YAAY,EAAE,OAAO,CAAC,MAAM;gBAC5B,UAAU,EAAE,MAAM,CAAC,MAAM;gBACzB,OAAO;gBACP,MAAM;gBACN,OAAO;aACR,CAAC;YAEF,GAAG,CAAC,IAAI,CAAC,sCAAsC,EAAE;gBAC/C,aAAa,EAAE,UAAU,CAAC,aAAa;gBACvC,YAAY,EAAE,UAAU,CAAC,YAAY;gBACrC,UAAU,EAAE,UAAU,CAAC,UAAU;gBACjC,mBAAmB,EAAE,OAAO,CAAC,mBAAmB;aACjD,CAAC,CAAC;YAEH,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,GAAG,CAAC,KAAK,CAAC,iCAAiC,EAAE,EAAE,eAAe,EAAE,EAAE,KAAc,CAAC,CAAC;YAClF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACrB,SAAyB,EAAE,EAC3B,aAA+B,EAAE;QAEjC,IAAI,CAAC;YACH,GAAG,CAAC,IAAI,CAAC,uBAAuB,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC;YAE1D,IAAI,aAAa,CAAC;YAElB,IAAI,MAAM,CAAC,SAAS,KAAK,KAAK,EAAE,CAAC;gBAC/B,uCAAuC;gBACvC,aAAa,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,uBAAuB,CAAC;oBAChE,SAAS,EAAE,MAAM,CAAC,SAAS;oBAC3B,QAAQ,EAAE,MAAM,CAAC,QAAQ;oBACzB,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,UAAU;iBACX,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,sBAAsB;gBACtB,aAAa,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAC1E,CAAC;YAED,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE;gBAC/B,KAAK,EAAE,aAAa,CAAC,aAAa,CAAC,MAAM;gBACzC,UAAU,EAAE,aAAa,CAAC,WAAW;aACtC,CAAC,CAAC;YAEH,OAAO;gBACL,aAAa,EAAE,aAAa,CAAC,aAAa;gBAC1C,UAAU,EAAE,aAAa,CAAC,WAAW;aACtC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,GAAG,CAAC,KAAK,CAAC,8BAA8B,EAAE,EAAE,MAAM,EAAE,EAAE,KAAc,CAAC,CAAC;YACtE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,0BAA0B,CAAC,YAAkC;QACnE,IAAI,YAAY,CAAC,KAAK,KAAK,MAAM,EAAE,CAAC;YAClC,OAAO,4BAA4B,CAAC;QACtC,CAAC;QAED,IAAI,YAAY,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YACrC,OAAO,yBAAyB,CAAC;QACnC,CAAC;QAED,MAAM,gBAAgB,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,YAAY,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QAC/F,IAAI,gBAAgB,GAAG,CAAC,EAAE,CAAC;YACzB,OAAO,yDAAyD,CAAC;QACnE,CAAC;QAED,OAAO,wCAAwC,CAAC;IAClD,CAAC;IAEO,2BAA2B,CAAC,OAAsC;QACxE,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO;gBACL,mBAAmB,EAAE,CAAC;gBACtB,YAAY,EAAE,EAAE;gBAChB,aAAa,EAAE,EAAE;gBACjB,kBAAkB,EAAE,EAAE;aACvB,CAAC;QACJ,CAAC;QAED,MAAM,mBAAmB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;QAE3G,qCAAqC;QACrC,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACjE,MAAM,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACnE,MAAM,kBAAkB,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;QAE7E,sCAAsC;QACtC,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QACvD,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACzD,MAAM,kBAAkB,GAAG,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QAEnE,OAAO;YACL,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,GAAG,CAAC,GAAG,GAAG;YAChE,YAAY;YACZ,aAAa;YACb,kBAAkB;SACnB,CAAC;IACJ,CAAC;IAEO,WAAW,CAAC,KAAe,EAAE,KAAa;QAChD,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YACxC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACjC,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAA4B,CAAC,CAAC;QAEjC,OAAO,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;aAC1B,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;aAC7B,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC;aACf,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;CACF"}