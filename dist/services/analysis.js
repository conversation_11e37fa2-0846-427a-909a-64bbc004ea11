/**
 * Analysis service that orchestrates Intercom and OpenAI services
 */
import { IntercomApiClient } from './intercom.js';
import { OpenAIAnalysisService } from './openai.js';
import { log } from '../utils/logger.js';
export class AnalysisService {
    intercomClient;
    openaiService;
    constructor() {
        this.intercomClient = new IntercomApiClient();
        this.openaiService = new OpenAIAnalysisService();
    }
    /**
     * Analyze quality of a single conversation
     */
    async analyzeConversationQuality(conversationId, config = {
        includeConversationParts: true,
        includeCustomerFeedback: true,
        analysisDepth: 'detailed',
    }) {
        try {
            log.info('Starting conversation quality analysis', { conversationId });
            // Fetch conversation from Intercom
            const conversation = await this.intercomClient.getConversation(conversationId);
            // Analyze with OpenAI
            const analysis = await this.openaiService.analyzeConversationQuality(conversation, {
                includeConversationParts: config.includeConversationParts,
                analysisDepth: config.analysisDepth,
                focusAreas: config.focusAreas || undefined,
            });
            log.info('Conversation quality analysis completed', {
                conversationId,
                overallScore: analysis.overallScore,
            });
            return analysis;
        }
        catch (error) {
            log.error('Failed to analyze conversation quality', { conversationId }, error);
            throw error;
        }
    }
    /**
     * Get conversations without customer ratings
     */
    async getUnratedConversations(filter = {}, pagination = {}) {
        try {
            log.info('Fetching unrated conversations', { filter, pagination });
            const conversations = await this.intercomClient.getUnratedConversations({
                dateRange: filter.dateRange || undefined,
                adminIds: filter.adminIds || undefined,
                status: filter.status || undefined,
                pagination,
            });
            const unratedConversations = conversations.conversations.map(conv => ({
                conversationId: conv.id,
                title: conv.title ?? undefined,
                createdAt: new Date(conv.created_at * 1000).toISOString(),
                updatedAt: new Date(conv.updated_at * 1000).toISOString(),
                adminAssignee: conv.admin_assignee_id ? {
                    id: conv.admin_assignee_id.toString(),
                    name: 'Unknown', // Would need to fetch admin details
                } : undefined,
                customerInfo: {
                    id: conv.contacts.contacts[0]?.id || 'unknown',
                    name: conv.contacts.contacts[0]?.name || undefined,
                    email: conv.contacts.contacts[0]?.email || undefined,
                },
                status: conv.state,
                messageCount: conv.conversation_parts?.total_count || 0,
                lastActivity: new Date(conv.updated_at * 1000).toISOString(),
                priority: conv.priority === 'priority' ? 'high' : 'normal',
                tags: conv.tags.tags.map(tag => tag.name),
                reasonForNoRating: this.determineReasonForNoRating(conv),
            }));
            log.info('Unrated conversations fetched', {
                count: unratedConversations.length,
                totalCount: conversations.total_count,
            });
            return unratedConversations;
        }
        catch (error) {
            log.error('Failed to fetch unrated conversations', { filter }, error);
            throw error;
        }
    }
    /**
     * Analyze support performance for a specific admin
     */
    async analyzeSupportPerformance(adminId, dateRange, _options = {}) {
        try {
            log.info('Starting support performance analysis', { adminId, dateRange });
            // Fetch admin details
            const admin = await this.intercomClient.getAdmin(adminId);
            // Fetch conversations for the admin
            const conversationsResponse = await this.intercomClient.getConversationsByAdmin(adminId, {
                dateRange,
                pagination: { per_page: 100 }, // Limit for performance
            });
            // Analyze performance with OpenAI
            const analysis = await this.openaiService.analyzeSupportPerformance(conversationsResponse.conversations, adminId, admin.name, dateRange);
            log.info('Support performance analysis completed', {
                adminId,
                conversationCount: conversationsResponse.conversations.length,
                qualityScore: analysis.metrics.qualityScore,
            });
            return analysis;
        }
        catch (error) {
            log.error('Failed to analyze support performance', { adminId, dateRange }, error);
            throw error;
        }
    }
    /**
     * Get detailed insights for a conversation
     */
    async getConversationInsights(conversationId, options = {}) {
        try {
            log.info('Getting conversation insights', { conversationId, options });
            // Fetch conversation from Intercom
            const conversation = await this.intercomClient.getConversation(conversationId);
            // Get insights with OpenAI
            const insights = await this.openaiService.getConversationInsights(conversation, options);
            log.info('Conversation insights generated', {
                conversationId,
                topicsCount: insights.topics.length,
                actionItemsCount: insights.actionItems.length,
            });
            return insights;
        }
        catch (error) {
            log.error('Failed to get conversation insights', { conversationId }, error);
            throw error;
        }
    }
    /**
     * Analyze multiple conversations in bulk
     */
    async bulkAnalyzeConversations(conversationIds, config = {
        includeConversationParts: true,
        includeCustomerFeedback: true,
        analysisDepth: 'detailed',
    }) {
        try {
            log.info('Starting bulk conversation analysis', {
                conversationCount: conversationIds.length,
                config,
            });
            const results = [];
            const errors = [];
            // Process conversations in batches to avoid overwhelming the APIs
            const batchSize = 5;
            for (let i = 0; i < conversationIds.length; i += batchSize) {
                const batch = conversationIds.slice(i, i + batchSize);
                const batchPromises = batch.map(async (conversationId) => {
                    try {
                        const analysis = await this.analyzeConversationQuality(conversationId, config);
                        results.push(analysis);
                    }
                    catch (error) {
                        errors.push({
                            conversationId,
                            error: error.message,
                        });
                    }
                });
                await Promise.all(batchPromises);
                // Add a small delay between batches to respect rate limits
                if (i + batchSize < conversationIds.length) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }
            // Generate summary
            const summary = this.generateBulkAnalysisSummary(results);
            const bulkResult = {
                totalAnalyzed: conversationIds.length,
                successCount: results.length,
                errorCount: errors.length,
                results,
                errors,
                summary,
            };
            log.info('Bulk conversation analysis completed', {
                totalAnalyzed: bulkResult.totalAnalyzed,
                successCount: bulkResult.successCount,
                errorCount: bulkResult.errorCount,
                averageQualityScore: summary.averageQualityScore,
            });
            return bulkResult;
        }
        catch (error) {
            log.error('Failed to perform bulk analysis', { conversationIds }, error);
            throw error;
        }
    }
    /**
     * List conversations with filtering
     */
    async listConversations(filter = {}, pagination = {}) {
        try {
            log.info('Listing conversations', { filter, pagination });
            let conversations;
            if (filter.hasRating === false) {
                // Use the unrated conversations method
                conversations = await this.intercomClient.getUnratedConversations({
                    dateRange: filter.dateRange || undefined,
                    adminIds: filter.adminIds || undefined,
                    status: filter.status || undefined,
                    pagination,
                });
            }
            else {
                // Use general listing
                conversations = await this.intercomClient.listConversations(pagination);
            }
            log.info('Conversations listed', {
                count: conversations.conversations.length,
                totalCount: conversations.total_count,
            });
            return {
                conversations: conversations.conversations,
                totalCount: conversations.total_count,
            };
        }
        catch (error) {
            log.error('Failed to list conversations', { filter }, error);
            throw error;
        }
    }
    determineReasonForNoRating(conversation) {
        if (conversation.state === 'open') {
            return 'Conversation is still open';
        }
        if (conversation.state === 'snoozed') {
            return 'Conversation is snoozed';
        }
        const daysSinceClosure = (Date.now() - conversation.updated_at * 1000) / (1000 * 60 * 60 * 24);
        if (daysSinceClosure < 1) {
            return 'Recently closed, customer may not have had time to rate';
        }
        return 'Customer chose not to provide a rating';
    }
    generateBulkAnalysisSummary(results) {
        if (results.length === 0) {
            return {
                averageQualityScore: 0,
                topStrengths: [],
                topWeaknesses: [],
                recommendedActions: [],
            };
        }
        const averageQualityScore = results.reduce((sum, result) => sum + result.overallScore, 0) / results.length;
        // Aggregate strengths and weaknesses
        const allStrengths = results.flatMap(result => result.strengths);
        const allWeaknesses = results.flatMap(result => result.weaknesses);
        const allRecommendations = results.flatMap(result => result.recommendations);
        // Count occurrences and get top items
        const topStrengths = this.getTopItems(allStrengths, 5);
        const topWeaknesses = this.getTopItems(allWeaknesses, 5);
        const recommendedActions = this.getTopItems(allRecommendations, 5);
        return {
            averageQualityScore: Math.round(averageQualityScore * 100) / 100,
            topStrengths,
            topWeaknesses,
            recommendedActions,
        };
    }
    getTopItems(items, limit) {
        const counts = items.reduce((acc, item) => {
            acc[item] = (acc[item] || 0) + 1;
            return acc;
        }, {});
        return Object.entries(counts)
            .sort(([, a], [, b]) => b - a)
            .slice(0, limit)
            .map(([item]) => item);
    }
}
//# sourceMappingURL=analysis.js.map