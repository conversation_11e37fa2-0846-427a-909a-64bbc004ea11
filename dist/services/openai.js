/**
 * OpenAI service for conversation analysis and insights
 */
import OpenAI from 'openai';
import { config } from '../utils/config.js';
import { log } from '../utils/logger.js';
import { sanitizeForAnalysis } from '../utils/validation.js';
export class OpenAIAnalysisService {
    client;
    constructor() {
        this.client = new OpenAI({
            apiKey: config.openai.apiKey,
        });
    }
    /**
     * Analyze conversation quality using OpenAI
     */
    async analyzeConversationQuality(conversation, options = {}) {
        const startTime = Date.now();
        try {
            log.analysis('quality-analysis', conversation.id, {
                analysisDepth: options.analysisDepth || 'detailed',
                focusAreas: options.focusAreas,
            });
            const conversationText = this.prepareConversationText(conversation, options.includeConversationParts);
            const prompt = this.buildQualityAnalysisPrompt(conversationText, options);
            const response = await this.client.chat.completions.create({
                model: config.openai.model,
                messages: [
                    {
                        role: 'system',
                        content: this.getQualityAnalysisSystemPrompt(),
                    },
                    {
                        role: 'user',
                        content: prompt,
                    },
                ],
                max_tokens: config.openai.maxTokens,
                temperature: config.openai.temperature,
                response_format: { type: 'json_object' },
            });
            const analysisResult = JSON.parse(response.choices[0]?.message?.content || '{}');
            const analysis = {
                conversationId: conversation.id,
                overallScore: analysisResult.overallScore || 5,
                dimensions: {
                    responsiveness: this.parseDimension(analysisResult.dimensions?.responsiveness),
                    helpfulness: this.parseDimension(analysisResult.dimensions?.helpfulness),
                    professionalism: this.parseDimension(analysisResult.dimensions?.professionalism),
                    problemResolution: this.parseDimension(analysisResult.dimensions?.problemResolution),
                    customerSatisfaction: this.parseDimension(analysisResult.dimensions?.customerSatisfaction),
                },
                summary: analysisResult.summary || 'Analysis completed',
                recommendations: analysisResult.recommendations || [],
                strengths: analysisResult.strengths || [],
                weaknesses: analysisResult.weaknesses || [],
                analysisTimestamp: new Date().toISOString(),
            };
            log.performance('quality-analysis', Date.now() - startTime, {
                conversationId: conversation.id,
                overallScore: analysis.overallScore,
            });
            return analysis;
        }
        catch (error) {
            log.error('Quality analysis failed', { conversationId: conversation.id }, error);
            throw new Error(`Failed to analyze conversation quality: ${error.message}`);
        }
    }
    /**
     * Analyze support performance for an admin
     */
    async analyzeSupportPerformance(conversations, adminId, adminName, period) {
        const startTime = Date.now();
        try {
            log.analysis('performance-analysis', adminId, {
                conversationCount: conversations.length,
                period,
            });
            const conversationSummaries = conversations.map(conv => this.summarizeConversation(conv));
            const prompt = this.buildPerformanceAnalysisPrompt(conversationSummaries, adminName, period);
            const response = await this.client.chat.completions.create({
                model: config.openai.model,
                messages: [
                    {
                        role: 'system',
                        content: this.getPerformanceAnalysisSystemPrompt(),
                    },
                    {
                        role: 'user',
                        content: prompt,
                    },
                ],
                max_tokens: config.openai.maxTokens,
                temperature: config.openai.temperature,
                response_format: { type: 'json_object' },
            });
            const analysisResult = JSON.parse(response.choices[0]?.message?.content || '{}');
            const analysis = {
                adminId,
                adminName,
                period,
                metrics: {
                    totalConversations: conversations.length,
                    averageResponseTime: this.calculateAverageResponseTime(conversations),
                    averageResolutionTime: this.calculateAverageResolutionTime(conversations),
                    customerSatisfactionScore: this.calculateCustomerSatisfactionScore(conversations),
                    qualityScore: analysisResult.metrics?.qualityScore || 5,
                },
                strengths: analysisResult.strengths || [],
                areasForImprovement: analysisResult.areasForImprovement || [],
                topIssuesHandled: analysisResult.topIssuesHandled || [],
                recommendations: analysisResult.recommendations || [],
                conversationExamples: {
                    best: analysisResult.conversationExamples?.best || [],
                    needsImprovement: analysisResult.conversationExamples?.needsImprovement || [],
                },
            };
            log.performance('performance-analysis', Date.now() - startTime, {
                adminId,
                conversationCount: conversations.length,
                qualityScore: analysis.metrics.qualityScore,
            });
            return analysis;
        }
        catch (error) {
            log.error('Performance analysis failed', { adminId }, error);
            throw new Error(`Failed to analyze support performance: ${error.message}`);
        }
    }
    /**
     * Get detailed conversation insights
     */
    async getConversationInsights(conversation, options = {}) {
        const startTime = Date.now();
        try {
            log.analysis('insights-analysis', conversation.id, options);
            const conversationText = this.prepareConversationText(conversation, true);
            const prompt = this.buildInsightsAnalysisPrompt(conversationText, options);
            const response = await this.client.chat.completions.create({
                model: config.openai.model,
                messages: [
                    {
                        role: 'system',
                        content: this.getInsightsAnalysisSystemPrompt(),
                    },
                    {
                        role: 'user',
                        content: prompt,
                    },
                ],
                max_tokens: config.openai.maxTokens,
                temperature: config.openai.temperature,
                response_format: { type: 'json_object' },
            });
            const analysisResult = JSON.parse(response.choices[0]?.message?.content || '{}');
            const insights = {
                conversationId: conversation.id,
                metadata: {
                    duration: this.calculateConversationDuration(conversation),
                    messageCount: conversation.conversation_parts?.total_count || 0,
                    participantCount: this.countParticipants(conversation),
                    tags: conversation.tags.tags.map(tag => tag.name),
                    priority: conversation.priority === 'priority' ? 'high' : 'normal',
                },
                sentiment: analysisResult.sentiment || {
                    overall: 'neutral',
                    progression: [],
                },
                topics: analysisResult.topics || [],
                resolution: {
                    status: conversation.state === 'closed' ? 'resolved' : 'unresolved',
                    timeToResolution: conversation.state === 'closed' ? this.calculateResolutionTime(conversation) : undefined,
                    resolutionQuality: analysisResult.resolution?.resolutionQuality || 5,
                },
                customerExperience: analysisResult.customerExperience || {
                    satisfactionIndicators: [],
                    frustrationIndicators: [],
                    engagementLevel: 'medium',
                },
                actionItems: analysisResult.actionItems || [],
                followUpNeeded: analysisResult.followUpNeeded || false,
            };
            log.performance('insights-analysis', Date.now() - startTime, {
                conversationId: conversation.id,
                topicsFound: insights.topics.length,
                actionItems: insights.actionItems.length,
            });
            return insights;
        }
        catch (error) {
            log.error('Insights analysis failed', { conversationId: conversation.id }, error);
            throw new Error(`Failed to get conversation insights: ${error.message}`);
        }
    }
    prepareConversationText(conversation, includeParts = true) {
        let text = `Conversation ID: ${conversation.id}\n`;
        text += `Title: ${conversation.title || 'No title'}\n`;
        text += `Status: ${conversation.state}\n`;
        text += `Priority: ${conversation.priority}\n`;
        text += `Created: ${new Date(conversation.created_at * 1000).toISOString()}\n`;
        if (conversation.conversation_rating) {
            text += `Customer Rating: ${conversation.conversation_rating.rating}/5\n`;
            if (conversation.conversation_rating.remark) {
                text += `Customer Feedback: ${sanitizeForAnalysis(conversation.conversation_rating.remark)}\n`;
            }
        }
        text += '\n--- Conversation Content ---\n';
        if (conversation.source?.body) {
            text += `Initial Message: ${sanitizeForAnalysis(conversation.source.body)}\n\n`;
        }
        if (includeParts && conversation.conversation_parts?.conversation_parts) {
            conversation.conversation_parts.conversation_parts.forEach((part, index) => {
                if (part.body) {
                    const author = part.author.type === 'admin' ? 'Support Agent' : 'Customer';
                    text += `${index + 1}. ${author}: ${sanitizeForAnalysis(part.body)}\n`;
                }
            });
        }
        return text;
    }
    parseDimension(dimension) {
        return {
            score: dimension?.score || 5,
            reasoning: dimension?.reasoning || 'No analysis available',
            examples: dimension?.examples || [],
        };
    }
    summarizeConversation(conversation) {
        return `ID: ${conversation.id}, Status: ${conversation.state}, Rating: ${conversation.conversation_rating?.rating || 'N/A'}, Messages: ${conversation.conversation_parts?.total_count || 0}`;
    }
    calculateAverageResponseTime(conversations) {
        const responseTimes = conversations
            .map(conv => conv.statistics?.time_to_admin_reply)
            .filter((time) => time !== undefined && time > 0);
        return responseTimes.length > 0
            ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length / 60 // Convert to minutes
            : 0;
    }
    calculateAverageResolutionTime(conversations) {
        const resolutionTimes = conversations
            .map(conv => conv.statistics?.time_to_first_close)
            .filter((time) => time !== undefined && time > 0);
        return resolutionTimes.length > 0
            ? resolutionTimes.reduce((sum, time) => sum + time, 0) / resolutionTimes.length / 60 // Convert to minutes
            : 0;
    }
    calculateCustomerSatisfactionScore(conversations) {
        const ratings = conversations
            .map(conv => conv.conversation_rating?.rating)
            .filter((rating) => rating !== undefined);
        return ratings.length > 0
            ? ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length
            : 0;
    }
    calculateConversationDuration(conversation) {
        if (conversation.statistics?.time_to_first_close) {
            return conversation.statistics.time_to_first_close / 60; // Convert to minutes
        }
        return (conversation.updated_at - conversation.created_at) / 60; // Convert to minutes
    }
    countParticipants(conversation) {
        const participants = new Set();
        if (conversation.source?.author) {
            participants.add(conversation.source.author.id);
        }
        conversation.conversation_parts?.conversation_parts?.forEach(part => {
            if (part.author) {
                participants.add(part.author.id);
            }
        });
        return participants.size;
    }
    calculateResolutionTime(conversation) {
        return conversation.statistics?.time_to_first_close
            ? conversation.statistics.time_to_first_close / 60 // Convert to minutes
            : undefined;
    }
    getQualityAnalysisSystemPrompt() {
        return `You are an expert customer service quality analyst. Analyze conversations between customers and support agents to provide detailed quality assessments.

Your analysis should be objective, constructive, and focused on improving customer experience and support effectiveness.

Return your analysis as a JSON object with the following structure:
{
  "overallScore": number (1-10),
  "dimensions": {
    "responsiveness": {"score": number (1-10), "reasoning": string, "examples": [string]},
    "helpfulness": {"score": number (1-10), "reasoning": string, "examples": [string]},
    "professionalism": {"score": number (1-10), "reasoning": string, "examples": [string]},
    "problemResolution": {"score": number (1-10), "reasoning": string, "examples": [string]},
    "customerSatisfaction": {"score": number (1-10), "reasoning": string, "examples": [string]}
  },
  "summary": string,
  "recommendations": [string],
  "strengths": [string],
  "weaknesses": [string]
}`;
    }
    buildQualityAnalysisPrompt(conversationText, options) {
        let prompt = `Please analyze the following customer support conversation for quality:\n\n${conversationText}\n\n`;
        if (options.analysisDepth === 'comprehensive') {
            prompt += 'Provide a comprehensive analysis including detailed examples and specific improvement suggestions.\n';
        }
        else if (options.analysisDepth === 'detailed') {
            prompt += 'Provide a detailed analysis with specific examples and actionable recommendations.\n';
        }
        else {
            prompt += 'Provide a basic quality assessment with key strengths and areas for improvement.\n';
        }
        if (options.focusAreas && options.focusAreas.length > 0) {
            prompt += `Focus particularly on these areas: ${options.focusAreas.join(', ')}\n`;
        }
        return prompt;
    }
    getPerformanceAnalysisSystemPrompt() {
        return `You are a customer service performance analyst. Analyze support agent performance across multiple conversations to identify patterns, strengths, and improvement opportunities.

Focus on quantifiable metrics, behavioral patterns, and actionable insights for professional development.

Return your analysis as a JSON object with the following structure:
{
  "metrics": {
    "qualityScore": number (1-10)
  },
  "strengths": [string],
  "areasForImprovement": [string],
  "topIssuesHandled": [string],
  "recommendations": [string],
  "conversationExamples": {
    "best": [{"conversationId": string, "score": number, "reason": string}],
    "needsImprovement": [{"conversationId": string, "score": number, "reason": string}]
  }
}`;
    }
    buildPerformanceAnalysisPrompt(conversationSummaries, adminName, period) {
        return `Analyze the performance of support agent "${adminName}" for the period ${period.startDate} to ${period.endDate}.

Conversation summaries:
${conversationSummaries.join('\n')}

Provide insights on their performance patterns, communication style, problem-solving approach, and areas for development.`;
    }
    getInsightsAnalysisSystemPrompt() {
        return `You are a conversation insights analyst. Extract meaningful insights from customer support conversations including sentiment, topics, resolution quality, and customer experience indicators.

Return your analysis as a JSON object with the following structure:
{
  "sentiment": {
    "overall": "positive" | "neutral" | "negative",
    "progression": [{"stage": "initial" | "middle" | "resolution", "sentiment": string, "confidence": number}]
  },
  "topics": [{"topic": string, "confidence": number, "mentions": number}],
  "resolution": {
    "resolutionQuality": number (1-10)
  },
  "customerExperience": {
    "satisfactionIndicators": [string],
    "frustrationIndicators": [string],
    "engagementLevel": "high" | "medium" | "low"
  },
  "actionItems": [string],
  "followUpNeeded": boolean
}`;
    }
    buildInsightsAnalysisPrompt(conversationText, options) {
        let prompt = `Analyze the following conversation for insights:\n\n${conversationText}\n\n`;
        if (options.includeTopics) {
            prompt += 'Identify key topics and themes discussed.\n';
        }
        if (options.includeSentiment) {
            prompt += 'Analyze sentiment progression throughout the conversation.\n';
        }
        if (options.includeActionItems) {
            prompt += 'Identify any action items or follow-up requirements.\n';
        }
        return prompt;
    }
}
//# sourceMappingURL=openai.js.map