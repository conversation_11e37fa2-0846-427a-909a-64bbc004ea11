/**
 * OpenAI service for conversation analysis and insights
 */
import type { ConversationQualityAnalysis, SupportPerformanceAnalysis, ConversationInsights, IntercomConversation } from '../types/index.js';
export declare class OpenAIAnalysisService {
    private client;
    constructor();
    /**
     * Analyze conversation quality using OpenAI
     */
    analyzeConversationQuality(conversation: IntercomConversation, options?: {
        includeConversationParts?: boolean;
        analysisDepth?: 'basic' | 'detailed' | 'comprehensive';
        focusAreas?: ('responsiveness' | 'helpfulness' | 'professionalism' | 'resolution')[];
    }): Promise<ConversationQualityAnalysis>;
    /**
     * Analyze support performance for an admin
     */
    analyzeSupportPerformance(conversations: IntercomConversation[], adminId: string, adminName: string, period: {
        startDate: string;
        endDate: string;
    }): Promise<SupportPerformanceAnalysis>;
    /**
     * Get detailed conversation insights
     */
    getConversationInsights(conversation: IntercomConversation, options?: {
        includeTopics?: boolean;
        includeSentiment?: boolean;
        includeActionItems?: boolean;
    }): Promise<ConversationInsights>;
    private prepareConversationText;
    private parseDimension;
    private summarizeConversation;
    private calculateAverageResponseTime;
    private calculateAverageResolutionTime;
    private calculateCustomerSatisfactionScore;
    private calculateConversationDuration;
    private countParticipants;
    private calculateResolutionTime;
    private getQualityAnalysisSystemPrompt;
    private buildQualityAnalysisPrompt;
    private getPerformanceAnalysisSystemPrompt;
    private buildPerformanceAnalysisPrompt;
    private getInsightsAnalysisSystemPrompt;
    private buildInsightsAnalysisPrompt;
}
//# sourceMappingURL=openai.d.ts.map