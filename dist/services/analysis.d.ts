/**
 * Analysis service that orchestrates Intercom and OpenAI services
 */
import type { ConversationQualityAnalysis, SupportPerformanceAnalysis, ConversationInsights, BulkAnalysisResult, UnratedConversation, AnalysisFilter, AnalysisConfig, IntercomConversation, PaginationParams } from '../types/index.js';
export declare class AnalysisService {
    private intercomClient;
    private openaiService;
    constructor();
    /**
     * Analyze quality of a single conversation
     */
    analyzeConversationQuality(conversationId: string, config?: AnalysisConfig): Promise<ConversationQualityAnalysis>;
    /**
     * Get conversations without customer ratings
     */
    getUnratedConversations(filter?: AnalysisFilter, pagination?: PaginationParams): Promise<UnratedConversation[]>;
    /**
     * Analyze support performance for a specific admin
     */
    analyzeSupportPerformance(adminId: string, dateRange: {
        startDate: string;
        endDate: string;
    }, _options?: {
        includeConversationExamples?: boolean;
        maxExamples?: number;
    }): Promise<SupportPerformanceAnalysis>;
    /**
     * Get detailed insights for a conversation
     */
    getConversationInsights(conversationId: string, options?: {
        includeTopics?: boolean;
        includeSentiment?: boolean;
        includeActionItems?: boolean;
    }): Promise<ConversationInsights>;
    /**
     * Analyze multiple conversations in bulk
     */
    bulkAnalyzeConversations(conversationIds: string[], config?: AnalysisConfig): Promise<BulkAnalysisResult>;
    /**
     * List conversations with filtering
     */
    listConversations(filter?: AnalysisFilter, pagination?: PaginationParams): Promise<{
        conversations: IntercomConversation[];
        totalCount: number;
    }>;
    private determineReasonForNoRating;
    private generateBulkAnalysisSummary;
    private getTopItems;
}
//# sourceMappingURL=analysis.d.ts.map