/**
 * Centralized type exports
 */
export * from './intercom.js';
export * from './analysis.js';
export interface ApiResponse<T> {
    success: boolean;
    data?: T;
    error?: string;
    timestamp: string;
}
export interface PaginationParams {
    page?: number;
    per_page?: number;
    starting_after?: string;
}
export interface SortParams {
    field: string;
    order: 'asc' | 'desc';
}
export interface FilterParams {
    [key: string]: string | number | boolean | string[] | number[] | undefined;
}
export interface RateLimitInfo {
    limit: number;
    remaining: number;
    resetTime: number;
}
export interface CacheEntry<T> {
    data: T;
    timestamp: number;
    ttl: number;
}
export interface LogContext {
    requestId?: string;
    userId?: string;
    conversationId?: string;
    operation?: string;
    [key: string]: any;
}
//# sourceMappingURL=index.d.ts.map