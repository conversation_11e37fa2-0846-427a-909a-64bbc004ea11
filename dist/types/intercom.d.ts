/**
 * Intercom API Types
 * Based on Intercom API v2.13 specification
 */
export interface IntercomConversation {
    type: 'conversation';
    id: string;
    title?: string | null;
    created_at: number;
    updated_at: number;
    waiting_since?: number | null;
    snoozed_until?: number | null;
    open: boolean;
    state: 'open' | 'closed' | 'snoozed';
    read: boolean;
    priority: 'priority' | 'not_priority';
    admin_assignee_id?: number | null;
    team_assignee_id?: string | null;
    tags: {
        type: 'tag.list';
        tags: IntercomTag[];
    };
    conversation_rating?: IntercomConversationRating | null;
    source: IntercomConversationSource;
    contacts: {
        type: 'contact.list';
        contacts: IntercomContact[];
    };
    teammates?: {
        type: 'admin.list';
        teammates: IntercomAdmin[];
    } | null;
    first_contact_reply?: {
        created_at: number;
        type: string;
        url?: string | null;
    } | null;
    statistics?: IntercomConversationStatistics | null;
    conversation_parts?: {
        type: 'conversation_part.list';
        conversation_parts: IntercomConversationPart[];
        total_count: number;
    };
    ai_agent_participated?: boolean;
}
export interface IntercomConversationRating {
    rating: number;
    remark?: string;
    created_at: number;
    contact: IntercomContact;
    teammate?: IntercomAdmin;
}
export interface IntercomConversationSource {
    type: string;
    id: string;
    delivered_as: string;
    subject?: string;
    body?: string;
    author?: IntercomAdmin | IntercomContact;
    attachments?: any[];
    url?: string | null;
    redacted: boolean;
}
export interface IntercomConversationPart {
    type: string;
    id: string;
    part_type: string;
    body?: string;
    created_at: number;
    updated_at: number;
    notified_at?: number;
    assigned_to?: IntercomAdmin | null;
    author: IntercomAdmin | IntercomContact;
    attachments?: any[];
    external_id?: string | null;
    redacted: boolean;
}
export interface IntercomConversationStatistics {
    type: 'conversation_statistics';
    time_to_assignment?: number;
    time_to_admin_reply?: number;
    time_to_first_close?: number;
    time_to_last_close?: number;
    median_time_to_reply?: number;
    first_contact_reply_at?: number;
    first_assignment_at?: number;
    first_admin_reply_at?: number;
    first_close_at?: number;
    last_assignment_at?: number;
    last_assignment_admin_reply_at?: number;
    last_contact_reply_at?: number;
    last_admin_reply_at?: number;
    last_close_at?: number;
    last_closed_by_id?: string;
    count_reopens?: number;
    count_assignments?: number;
    count_conversation_parts?: number;
    handling_time?: number;
}
export interface IntercomContact {
    type: 'contact';
    id: string;
    workspace_id?: string;
    external_id?: string | null;
    role: string;
    email?: string;
    phone?: string | null;
    name?: string | null;
    avatar?: string | null;
    owner_id?: number | null;
    social_profiles?: any;
    has_hard_bounced?: boolean;
    marked_email_as_spam?: boolean;
    unsubscribed_from_emails?: boolean;
    created_at: number;
    updated_at: number;
    signed_up_at?: number | null;
    last_seen_at?: number | null;
    last_replied_at?: number | null;
    last_contacted_at?: number | null;
    last_email_opened_at?: number | null;
    last_email_clicked_at?: number | null;
    language_override?: string | null;
    browser?: string | null;
    browser_version?: string | null;
    browser_language?: string | null;
    os?: string | null;
    location?: any;
    android_app_name?: string | null;
    android_app_version?: string | null;
    android_device?: string | null;
    android_os_version?: string | null;
    android_sdk_version?: string | null;
    android_last_seen_at?: number | null;
    ios_app_name?: string | null;
    ios_app_version?: string | null;
    ios_device?: string | null;
    ios_os_version?: string | null;
    ios_sdk_version?: string | null;
    ios_last_seen_at?: number | null;
    custom_attributes?: Record<string, any>;
    tags?: {
        type: 'tag.list';
        tags: IntercomTag[];
    };
    notes?: {
        type: 'note.list';
        notes: any[];
    };
    companies?: {
        type: 'company.list';
        companies: any[];
    };
}
export interface IntercomAdmin {
    type: 'admin';
    id: string;
    name: string;
    email: string;
    job_title?: string;
    away_mode_enabled?: boolean;
    away_mode_reassign?: boolean;
    has_inbox_seat?: boolean;
    team_ids?: string[];
    avatar?: string;
}
export interface IntercomTag {
    type: 'tag';
    id: string;
    name: string;
    applied_at?: number;
    applied_by?: {
        type: string;
        id: string;
    };
}
export interface IntercomConversationList {
    type: 'conversation.list';
    conversations: IntercomConversation[];
    total_count: number;
    pages?: {
        type: 'pages';
        page: number;
        per_page: number;
        total_pages: number;
    };
}
export interface IntercomSearchQuery {
    query: {
        operator: 'AND' | 'OR';
        operands: Array<{
            field: string;
            operator: string;
            value: string | number | boolean;
        }>;
    };
    pagination?: {
        per_page?: number;
        starting_after?: string;
    };
    sort?: {
        field: string;
        order: 'ascending' | 'descending';
    };
}
export interface IntercomApiError {
    type: 'error.list';
    request_id: string;
    errors: Array<{
        code: string;
        message: string;
    }>;
}
//# sourceMappingURL=intercom.d.ts.map