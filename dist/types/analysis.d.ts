/**
 * Analysis Types for OpenAI-powered conversation analysis
 */
export interface ConversationQualityAnalysis {
    conversationId: string;
    overallScore: number;
    dimensions: {
        responsiveness: QualityDimension;
        helpfulness: QualityDimension;
        professionalism: QualityDimension;
        problemResolution: QualityDimension;
        customerSatisfaction: QualityDimension;
    };
    summary: string;
    recommendations: string[];
    strengths: string[];
    weaknesses: string[];
    analysisTimestamp: string;
}
export interface QualityDimension {
    score: number;
    reasoning: string;
    examples?: string[];
}
export interface SupportPerformanceAnalysis {
    adminId: string;
    adminName: string;
    period: {
        startDate: string;
        endDate: string;
    };
    metrics: {
        totalConversations: number;
        averageResponseTime: number;
        averageResolutionTime: number;
        customerSatisfactionScore: number;
        qualityScore: number;
    };
    strengths: string[];
    areasForImprovement: string[];
    topIssuesHandled: string[];
    recommendations: string[];
    conversationExamples: {
        best: ConversationExample[];
        needsImprovement: ConversationExample[];
    };
}
export interface ConversationExample {
    conversationId: string;
    title?: string;
    score: number;
    reason: string;
    customerFeedback?: string;
}
export interface ConversationInsights {
    conversationId: string;
    metadata: {
        duration: number;
        messageCount: number;
        participantCount: number;
        tags: string[];
        priority: 'high' | 'normal' | 'low';
    };
    sentiment: {
        overall: 'positive' | 'neutral' | 'negative';
        progression: Array<{
            stage: 'initial' | 'middle' | 'resolution';
            sentiment: 'positive' | 'neutral' | 'negative';
            confidence: number;
        }>;
    };
    topics: Array<{
        topic: string;
        confidence: number;
        mentions: number;
    }>;
    resolution: {
        status: 'resolved' | 'unresolved' | 'escalated';
        timeToResolution?: number | undefined;
        resolutionQuality: number;
    };
    customerExperience: {
        satisfactionIndicators: string[];
        frustrationIndicators: string[];
        engagementLevel: 'high' | 'medium' | 'low';
    };
    actionItems: string[];
    followUpNeeded: boolean;
}
export interface BulkAnalysisResult {
    totalAnalyzed: number;
    successCount: number;
    errorCount: number;
    results: ConversationQualityAnalysis[];
    errors: Array<{
        conversationId: string;
        error: string;
    }>;
    summary: {
        averageQualityScore: number;
        topStrengths: string[];
        topWeaknesses: string[];
        recommendedActions: string[];
    };
}
export interface UnratedConversation {
    conversationId: string;
    title?: string;
    createdAt: string;
    updatedAt: string;
    adminAssignee?: {
        id: string;
        name: string;
    };
    customerInfo: {
        id: string;
        name?: string;
        email?: string;
    };
    status: 'open' | 'closed' | 'snoozed';
    messageCount: number;
    lastActivity: string;
    priority: 'high' | 'normal' | 'low';
    tags: string[];
    estimatedSatisfaction?: number;
    reasonForNoRating?: string;
}
export interface AnalysisFilter {
    dateRange?: {
        startDate: string;
        endDate: string;
    };
    adminIds?: string[];
    tags?: string[];
    priority?: ('high' | 'normal' | 'low')[];
    status?: ('open' | 'closed' | 'snoozed')[];
    hasRating?: boolean;
    minQualityScore?: number;
    maxQualityScore?: number;
}
export interface AnalysisConfig {
    includeConversationParts: boolean;
    includeCustomerFeedback: boolean;
    analysisDepth: 'basic' | 'detailed' | 'comprehensive';
    focusAreas?: ('responsiveness' | 'helpfulness' | 'professionalism' | 'resolution')[];
}
export type AnalysisStatus = 'pending' | 'in_progress' | 'completed' | 'failed';
export interface AnalysisJob {
    id: string;
    type: 'single' | 'bulk' | 'performance';
    status: AnalysisStatus;
    progress: number;
    startedAt: string;
    completedAt?: string;
    config: AnalysisConfig;
    filter?: AnalysisFilter;
    results?: ConversationQualityAnalysis | BulkAnalysisResult | SupportPerformanceAnalysis;
    error?: string;
}
//# sourceMappingURL=analysis.d.ts.map