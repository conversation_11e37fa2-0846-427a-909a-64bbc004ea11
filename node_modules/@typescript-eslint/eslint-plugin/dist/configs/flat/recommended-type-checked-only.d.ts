import type { FlatConfig } from '@typescript-eslint/utils/ts-eslint';
/**
 * A version of `recommended` that only contains type-checked rules and disables of any corresponding core ESLint rules.
 * @see {@link https://typescript-eslint.io/users/configs#recommended-type-checked-only}
 */
declare const _default: (plugin: FlatConfig.Plugin, parser: FlatConfig.Parser) => FlatConfig.ConfigArray;
export default _default;
//# sourceMappingURL=recommended-type-checked-only.d.ts.map