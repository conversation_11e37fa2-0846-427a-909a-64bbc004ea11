import { ESLintUtils } from '@typescript-eslint/utils';
import type { ESLintPluginDocs } from '../../rules';
export declare const createRule: <Options extends readonly unknown[], MessageIds extends string>({ meta, name, ...rule }: Readonly<ESLintUtils.RuleWithMetaAndName<Options, MessageIds, ESLintPluginDocs>>) => ESLintUtils.RuleModule<MessageIds, Options, ESLintPluginDocs, ESLintUtils.RuleListener>;
//# sourceMappingURL=createRule.d.ts.map