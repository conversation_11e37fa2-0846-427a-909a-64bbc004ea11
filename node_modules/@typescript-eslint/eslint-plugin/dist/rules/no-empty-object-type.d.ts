import type { TSESLint } from '@typescript-eslint/utils';
export type AllowInterfaces = 'always' | 'never' | 'with-single-extends';
export type AllowObjectTypes = 'always' | 'never';
export type Options = [
    {
        allowInterfaces?: AllowInterfaces;
        allowObjectTypes?: AllowObjectTypes;
        allowWithName?: string;
    }
];
export type MessageIds = 'noEmptyInterface' | 'noEmptyInterfaceWithSuper' | 'noEmptyObject' | 'replaceEmptyInterface' | 'replaceEmptyInterfaceWithSuper' | 'replaceEmptyObjectType';
declare const _default: TSESLint.RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, TSESLint.RuleListener>;
export default _default;
//# sourceMappingURL=no-empty-object-type.d.ts.map