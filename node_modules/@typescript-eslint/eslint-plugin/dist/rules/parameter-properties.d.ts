type Modifier = 'private' | 'private readonly' | 'protected' | 'protected readonly' | 'public' | 'public readonly' | 'readonly';
type Prefer = 'class-property' | 'parameter-property';
export type Options = [
    {
        allow?: Modifier[];
        prefer?: Prefer;
    }
];
export type MessageIds = 'preferClassProperty' | 'preferParameterProperty';
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=parameter-properties.d.ts.map